{"expo": {"name": "SicrediApp", "slug": "sicredi-app", "version": "1.0.0", "extra": {"eas": {"projectId": "a9c98ad7-2a60-45c8-8e4e-f240ce661bfa"}}, "plugins": [["expo-notifications", {"mode": "production"}], ["expo-font", {"fonts": ["./src/assets/fonts/Calibri.ttf", "./src/assets/fonts/Exo2.0-Regular.otf", "./src/assets/fonts/Exo2.0-Bold.otf", "./src/assets/fonts/Nunito-Regular.ttf", "./src/assets/fonts/Nunito-Light.ttf"]}], ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-dev-client", ["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}], ["expo-local-authentication", {"faceIDPermission": "Allow $(PRODUCT_NAME) to use Face ID."}]], "android": {"permissions": ["android.permission.RECORD_AUDIO", "NOTIFICATIONS"], "package": "com.sicrediApp", "useNextNotificationsApi": true, "googleServicesFile": "./google-services.json"}, "ios": {"bundleIdentifier": "com.sicrediApp", "infoPlist": {"NSUserNotificationUsageDescription": "Este aplicativo usa notificações para enviar alertas importantes.", "UIBackgroundModes": ["fetch", "remote-notification"], "NSMicrophoneUsageDescription": "This app needs access to your microphone to record audio.", "NSSpeechRecognitionUsageDescription": "This app needs access to speech recognition to process your speech commands.", "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}}, "newArchEnabled": false}}