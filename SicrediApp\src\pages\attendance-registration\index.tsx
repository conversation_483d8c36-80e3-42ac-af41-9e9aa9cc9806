import React, { use<PERSON><PERSON>back, useState } from "react";
import {
  <PERSON><PERSON>er<PERSON><PERSON>,
  InputWrapper,
  Row,
  MicButton,
  Mic<PERSON><PERSON>per,
  InputDescription,
  InputBox,
  TitleContainer,
  ListWrapper,
  ProductColumn,
  ValueColumn,
  DeleteColumn,
  DeleteButton,
  DateColumn,
  DescriptionColumn,
  DateBox,
  CustomRow,
} from "./styles";
import Input from "../../components/input";
import Button from "../../components/button";
import DefaultHeader from "../../components/defaultHeader";
import { useToaster } from "../../contexts/ToasterContext";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootStackParamList } from "../../routes/navigationType";
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import normalize, {
  stringMonetaryValue,
  stringToNumber,
} from "../../utils/normalize";
import { AttendanceType } from "../../types/Attendance.types";
import { AttendanceProductType } from "../../types/AttendanceProduct.types";
import { defaultAttendanceStatusList } from "../../types/AttendanceStatus.types";
import { createAttendance } from "../../services/attendanceService";
import { useUser } from "../../contexts/UserContext";
import {
  BoldWeight,
  Grey800,
  PrimaryText,
  SmallText,
  TitleText,
} from "../../components/text/styles";
import ImageComponent from "../../components/image";
import { useTheme } from "styled-components";
import SvgComponent from "../../components/svg";
import SvgIcons from "../../assets/icons/SvgIcons";
import { ProductType } from "../../types/Product.types";
import SelectInput from "../../components/selectInput";
import DefaultForm from "../../components/defaultForm";
import { ScheduleType } from "../../types/AttendanceSchedule.types";
import DatePickerInput from "../../components/datePickerInput";
// import {
//   ExpoSpeechRecognitionModule,
//   useSpeechRecognitionEvent,
// } from "expo-speech-recognition";
import { TouchableOpacity } from "react-native-gesture-handler";
import { useAttendance } from "../../contexts/AttendanceContext";

type NavigationProp = StackNavigationProp<RootStackParamList>;

export default function AttendanceRegistration() {
  const person = require("../../assets/images/16px-39-segmentos-pessoa-fisica.png");
  const route =
    useRoute<RouteProp<RootStackParamList, "AttendanceRegistration">>();
  const associateId = route.params?.associateId;
  const associateName = route.params?.associateName;
  const accountId = route.params?.accountId;
  const { user, updateUser, products } = useUser();
  const { colors } = useTheme();
  const { products: attendanceProductsList, schedules: attendanceSchedulesList, attendance } = useAttendance();
  const [productsList, setProductsList] = useState<ProductType[]>([]);
  const [productsListFull, setProductsListFull] = useState<ProductType[]>([]);
  const [productsListSelected, setProductsListSelected] = useState<
    ProductType[]
  >([]);
  const [attendanceProducts, setAttendanceProducts] = useState<
    AttendanceProductType[]
  >([]);
  const [attendanceSchedules, setAttendanceSchedules] = useState<
    ScheduleType[]
  >([]);
  const [status, setStatus] = useState("IN_PROGRESS");
  const [description, setDescription] = useState("");
  const showCast = useToaster();
  const navigation = useNavigation<NavigationProp>();
  const [recognizing, setRecognizing] = useState(false);

  // useSpeechRecognitionEvent("start", () => setRecognizing(true));
  // useSpeechRecognitionEvent("end", () => setRecognizing(false));
  // useSpeechRecognitionEvent("result", (event) => {
  //   setDescription(description + event.results[0]?.transcript);
  // });
  // useSpeechRecognitionEvent("error", (event) => {
  //   setDescription(
  //     "error code: " + event.error + " error message: " + event.message
  //   );
  //   console.log("error code:", event.error, "error message:", event.message);
  // });

  const handleStart = async () => {
    // const result = await ExpoSpeechRecognitionModule.requestPermissionsAsync();
    // if (!result.granted) {
    //   console.warn("Permissions not granted", result);
    //   return;
    // }
    // ExpoSpeechRecognitionModule.start({
    //   lang: "pt-BR",
    //   interimResults: false,
    //   maxAlternatives: 1,
    //   continuous: true,
    //   requiresOnDeviceRecognition: false,
    //   addsPunctuation: false,
    //   androidIntentOptions: {
    //     EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS: 100000000,
    //   },
    //   recordingOptions: {
    //     persist: false,
    //     outputDirectory: undefined,
    //     outputFileName: "recording.wav",
    //     outputSampleRate: undefined,
    //     outputEncoding: undefined,
    //   },
    // });
  };

  async function handleListening() {
    try {
      if (recognizing) {
        // ExpoSpeechRecognitionModule.stop();
        setRecognizing(false);
      } else {
        setDescription("");
        handleStart();
      }
    } catch (error: any) {
      showCast.showToast("error", "Atenção!", error.message);
    }
  }

  const handleAssociateWallets = async () => {
    navigation.navigate("AssociatesWallets");
  };

  const handleRegister = async () => {
    try {
      const hasEmptySchedule = attendanceSchedules.some(
        (item) => !item.date || !item.description?.trim()
      );

      if (hasEmptySchedule) {
        showCast.showToast(
          "error",
          "Atenção!",
          "Descrição do agendamento obrigatório."
        );
        return;
      }
      const cleanProducts = attendanceProducts
        .filter((item) => item?.product?.id)
        .map((item) => {
          const cleanItem = { ...item };
          cleanItem.productId = cleanItem.product?.id;
          cleanItem.valueOrQuantity = cleanItem.product?.valueOrQuantity;
          cleanItem.attendanceId = user.id;
          if (cleanItem.productValue === "") {
            delete cleanItem.productValue;
            cleanItem.quantity =
              stringToNumber(cleanItem.quantity?.toString() ?? "1") ?? "1";
          } else if (cleanItem.quantity === "") {
            delete cleanItem.quantity;
            if (cleanItem.productValue != null) {
              cleanItem.productValue =
                stringMonetaryValue(cleanItem.productValue) ?? "0";
            } else {
              cleanItem.productValue = "0";
            }
          }
          delete cleanItem.product;

          return cleanItem;
        });

      const attendance: AttendanceType = await normalize.cleanObjectData({
        attendanceProducts: cleanProducts,
        attendanceSchedules: attendanceSchedules
          .filter((item) => item?.date && item?.description)
          .map((item) => ({
            ...item,
            scheduleAt: item.date,
            attendanceId: user.id,
          })),
        statusCode: status,
        description,
        associateId,
        attendantId: user.id,
        accountId,
      } as AttendanceType);

      const { message } = await createAttendance(attendance);
      showCast.showToast("success", "Sucesso!", message);
      updateUser({ hasAttendedToday: true });
      handleAssociateWallets();
    } catch (error: any) {
      showCast.showToast("error", "Atenção!", error.message);
    }
  };

  const handleUpdateProduct = (
    index: number,
    field: "quantity" | "productValue",
    value: string
  ) => {
    setAttendanceProducts((prev) =>
      prev.map((product, i) =>
        i === index ? { ...product, [field]: value } : product
      )
    );
  };

  const handleRemoveProduct = (index: number) => {
    setAttendanceProducts((prev) => prev.filter((_, i) => i !== index));
  };

  const handleProductChange = (selectedProductId: string, index: number) => {
    const selectedProduct = productsList.find(
      (product) => product.id?.toString() === selectedProductId
    );

    if (selectedProduct) {
      setProductsListSelected((prev) => [...prev, selectedProduct]);
      setAttendanceProducts((prev) => {
        const updated = [...prev];
        updated[index] = {
          ...updated[index],
          product: selectedProduct,
        };
        return updated;
      });
    }
  };

  const handleAddProduct = () => {
    setAttendanceProducts((prev) => [
      ...prev,
      { product: {}, productValue: "", quantity: "" },
    ]);
  };

  const handleRemoveSchedule = (index: number) => {
    setAttendanceSchedules((prev) => prev.filter((_, i) => i !== index));
  };

  const handleScheduleChange = (
    index: number,
    field: "date" | "description" | "title",
    value: Date | string
  ) => {
    setAttendanceSchedules((prev) =>
      prev.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    );
  };

  const handleAddSchedule = () => {
    setAttendanceSchedules((prev) => [
      ...prev,
      { date: new Date(), description: "" },
    ]);
  };

  const getProductsListFull = async () => {
    try {
      const list = products;
      setProductsListFull(list);
      setProductsList(list.slice(0, 50));
    } catch (error: any) {
      showCast.showToast(
        "error",
        "Erro!",
        "Falha ao carregar Lista!, tente novamente."
      );
    }
  };

  function mergeProductArrays(
    filteredProducts: ProductType[],
    selectedProducts: ProductType[]
  ) {
    const selectedIds = new Set(selectedProducts.map((product) => product.id));
    const uniqueFiltered = filteredProducts.filter(
      (product) => !selectedIds.has(product.id)
    );
    const finalProducts = [...uniqueFiltered, ...selectedProducts];
    return finalProducts;
  }

  const getProductsList = async (value: string) => {
    try {
      const list = productsListFull.filter((item) =>
        item?.name?.toLowerCase().includes(value.toLowerCase())
      );
      const listProducts = mergeProductArrays(
        list.slice(0, 50),
        productsListSelected
      );
      setProductsList(listProducts);
    } catch (error: any) {
      showCast.showToast(
        "error",
        "Erro!",
        "Falha ao carregar Lista!, tente novamente."
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      getProductsListFull();
    }, [])
  );

  return (
    <>
      <DefaultHeader title="Registro" showBackBtn />
      <DefaultForm
        submitText="Finalizar"
        onSubmit={handleRegister}
        disableSubmit={!description}
      >
        <ContainerCenter>
          <TitleContainer>
            <TitleText>
              <Grey800>
                <BoldWeight>
                  <ImageComponent image={person} width={24} height={24} />{" "}
                  {associateName}
                </BoldWeight>
              </Grey800>
            </TitleText>
          </TitleContainer>
          <Row>
            <PrimaryText>Produtos Contratados</PrimaryText>
            <Button
              text="Adicionar"
              onPress={() => handleAddProduct()}
              width="28%"
              height="35px"
              disable={status === "NO_CONTACT"}
            />
          </Row>

          {/* Products */}
          <ListWrapper>
            {attendanceProducts.length > 0 ? (
              attendanceProducts.map((item, index) => (
                <CustomRow key={index}>
                  <ProductColumn>
                    <SelectInput
                      value={item.product?.id?.toString() ?? ""}
                      options={productsList.map((product) => ({
                        label: product.name ?? "",
                        value: product.id?.toString() ?? "",
                      }))}
                      label={"Produto"}
                      onChange={(selectedValue) =>
                        handleProductChange(selectedValue, index)
                      }
                      placeholder="Selecione um Produto"
                      size="small"
                      showSearchBar
                      onSearch={(value: string) => {
                        getProductsList(value);
                      }}
                    />
                  </ProductColumn>
                  {item.product?.valueOrQuantity === 1 ? (
                    <ValueColumn>
                      <Input
                        label="Quantidade"
                        placeholder="0"
                        value={item.quantity?.toString() ?? ""}
                        keyboardType="numeric"
                        onChangeText={(text) =>
                          handleUpdateProduct(index, "quantity", text)
                        }
                        size="small"
                      />
                    </ValueColumn>
                  ) : (
                    <ValueColumn>
                      <Input
                        label="Valor"
                        placeholder="0,00"
                        value={item.productValue ?? ""}
                        type="currency"
                        onChangeText={(text) =>
                          handleUpdateProduct(index, "productValue", text)
                        }
                        size="small"
                      />
                    </ValueColumn>
                  )}
                  <DeleteColumn>
                    <DeleteButton onPress={() => handleRemoveProduct(index)}>
                      <SvgComponent
                        d={SvgIcons.trash_can_icon}
                        width={18}
                        height={18}
                        viewBox="0 0 24 24"
                        stroke={colors.white}
                        strokeWidth={2}
                      />
                    </DeleteButton>
                  </DeleteColumn>
                </CustomRow>
              ))
            ) : (
              <ContainerCenter>
                <SmallText>Nenhum produto adicionado.</SmallText>
              </ContainerCenter>
            )}
          </ListWrapper>

          <SelectInput
            value={status}
            options={defaultAttendanceStatusList.map((status) => ({
              label: status.description ?? "",
              value: status.key ?? "",
            }))}
            label={"Status"}
            onChange={setStatus}
            placeholder="Em aberto"
          />

          <Row>
            <PrimaryText>Agendamentos</PrimaryText>
            <Button
              text="Adicionar"
              onPress={() => handleAddSchedule()}
              width="28%"
              height="35px"
              disable={status === "NO_CONTACT"}
            />
          </Row>

          {/* Schedules */}
          <ListWrapper>
            {attendanceSchedules.length > 0 ? (
              attendanceSchedules.map((item, index) => (
                <CustomRow key={index} alignItems="left">
                  <DateBox>
                    <DateColumn>
                      <DatePickerInput
                        label="Data"
                        value={item.date ? item.date : new Date()}
                        onChange={(date) =>
                          handleScheduleChange(index, "date", date)
                        }
                        size={"small"}
                        mode="datetime"
                      />
                    </DateColumn>
                    <DescriptionColumn>
                      <Input
                        label="Titulo"
                        placeholder="Digite o titulo"
                        value={item.title ?? ""}
                        keyboardType="default"
                        onChangeText={(text) =>
                          handleScheduleChange(index, "title", text)
                        }
                        size="small"
                      />
                    </DescriptionColumn>
                    <DescriptionColumn>
                      <Input
                        label="Descrição"
                        placeholder="Digite a Descrição"
                        value={item.description ?? ""}
                        keyboardType="default"
                        onChangeText={(text) =>
                          handleScheduleChange(index, "description", text)
                        }
                        size="small"
                      />
                    </DescriptionColumn>
                  </DateBox>
                  <DeleteColumn>
                    <DeleteButton onPress={() => handleRemoveSchedule(index)}>
                      <SvgComponent
                        d={SvgIcons.trash_can_icon}
                        width={18}
                        height={18}
                        viewBox="0 0 24 24"
                        stroke={colors.white}
                        strokeWidth={2}
                      />
                    </DeleteButton>
                  </DeleteColumn>
                </CustomRow>
              ))
            ) : (
              <ContainerCenter>
                <SmallText>Nenhum agendamento adicionado.</SmallText>
              </ContainerCenter>
            )}
          </ListWrapper>

          <MicWrapper>
            {!recognizing ? 
              (<PrimaryText>Aperte para Falar</PrimaryText>) : 
              (<PrimaryText>Ouvindo...</PrimaryText>)
            }
            <MicButton onPress={handleListening}>
              <SvgComponent
                d={!recognizing ? SvgIcons.microphone_icon : "M9 1L3.5 6.5L1 4"}
                width={!recognizing ? 60 : 40}
                height={!recognizing ? 60 : 40}
                strokeWidth={1}
                viewBox={!recognizing ? "-17 -10 60 60" : "0 -2 10 10"}
                stroke={colors.green}
              />
            </MicButton>
          </MicWrapper>

          <InputWrapper>
            <PrimaryText>Transcrição</PrimaryText>
            <InputBox
              isFocused={false}
              style={{ flexDirection: "row", alignItems: "center" }}
            >
              <InputDescription
                editable={!recognizing}
                placeholder="Digite ou aperte para falar..."
                value={description}
                onChangeText={setDescription}
                placeholderTextColor={colors.grey_400}
                maxLength={1000}
                multiline={true}
                numberOfLines={4}
                style={{ flex: 1 }}
              />
              {description ? (
                <TouchableOpacity
                  style={{ marginHorizontal: 15 }}
                  onPress={() => setDescription("")}
                >
                  <SvgComponent
                    d={SvgIcons.close_icon}
                    width={28}
                    height={28}
                    viewBox="0 0 24 24"
                    stroke={colors.grey_400}
                    strokeWidth={2}
                  />
                </TouchableOpacity>
              ) : null}
            </InputBox>
          </InputWrapper>
        </ContainerCenter>
      </DefaultForm>
    </>
  );
}
