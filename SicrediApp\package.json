{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@babel/plugin-transform-async-generator-functions": "^7.25.8", "@babel/plugin-transform-logical-assignment-operators": "^7.25.8", "@babel/plugin-transform-numeric-separator": "^7.25.8", "@expo/config-plugins": "~9.0.0", "@expo/prebuild-config": "~8.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli": "^17.0.0", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "axios": "^1.7.7", "dayjs": "^1.11.13", "expo": "~52.0.38", "expo-dev-client": "~5.0.12", "expo-device": "~7.0.2", "expo-font": "~13.0.2", "expo-linear-gradient": "~14.0.2", "expo-local-authentication": "~15.0.2", "expo-module-scripts": "^3.5.2", "expo-notifications": "~0.29.14", "expo-secure-store": "~14.0.1", "expo-status-bar": "~2.0.1", "moment": "^2.30.1", "node-forge": "^1.3.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-responsive-fontsize": "^0.5.1", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-select-dropdown": "^4.0.1", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.2.1", "send": "^1.1.0", "styled-components": "^6.1.13", "victory-native": "^37.3.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/node-forge": "^1.3.11", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "@types/styled-components": "^5.1.34", "expo-modules-core": "^2.2.2", "typescript": "~5.3.3"}, "private": true}